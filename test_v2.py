import os
import cv2
import glob
import numpy as np
from pathlib import Path
from typing import Dict, Any


SD = (640, 480)
HD = (1280, 720)
FHD = (1920, 1080)
UHD = (3840, 2160)

SQUARE_SIZE = 0.08     # Real-world square size in meters
CHECKERBOARD_TV = (16, 9)

objp_tv = np.zeros((CHECKERBOARD_TV[0] * CHECKERBOARD_TV[1], 3), np.float32)
objp_tv[:, :2] = np.mgrid[0:CHECKERBOARD_TV[0], 0:CHECKERBOARD_TV[1]].T.reshape(-1, 2)
objp_tv *= SQUARE_SIZE     # Scale according to real square size
objp_tv[:, :2] = objp_tv[:, :2] - SQUARE_SIZE/2     # translate



"""
camera_6:
   camera_matrix: !!opencv-matrix
      rows: 3
      cols: 3
      dt: d
      data: [ 1394.5957807561888, 0., 958.74500166610699, 0.,
          1404.9668068248047, 555.20462610570939, 0., 0., 1. ]
   distortion_vector: !!opencv-matrix
      rows: 1
      cols: 5
      dt: d
      data: [ -0.41769643352953612, 0.25445773410103223,
          -1.2780172776273487e-05, 0.00050445572790899058,
          -0.096652284531142732 ]
   distortion_type: 0
   camera_group: 0
   img_width: 1920
   img_height: 1080
   camera_pose_matrix: !!opencv-matrix
      rows: 4
      cols: 4
      dt: d
      data: [ -0.98580347756608722, 0.12947809475858663,
          -0.10689680348960764, 659.83036837608643,
          -0.0098345182828659828, 0.59103727524671912,
          0.80658429287897637, -1605.6979376834024, 0.16761499297125332,
          0.79618487943857674, -0.58137324661926604, 2190.1733759689978,
          0., 0., 0., 1. ]
camera_7:
   camera_matrix: !!opencv-matrix
      rows: 3
      cols: 3
      dt: d
      data: [ 1395.8112776021057, 0., 895.77357834609677, 0.,
          1408.4420526057102, 522.74286341290906, 0., 0., 1. ]
   distortion_vector: !!opencv-matrix
      rows: 1
      cols: 5
      dt: d
      data: [ -0.41497026814426491, 0.26389788187134555,
          0.00036737516526464959, 0.0015406335090577274,
          -0.1122178180041801 ]
   distortion_type: 0
   camera_group: 0
   img_width: 1920
   img_height: 1080
   camera_pose_matrix: !!opencv-matrix
      rows: 4
      cols: 4
      dt: d
      data: [ -0.97164438233867023, -0.048978129866226794,
          0.23131869155887674, -900.82543764430659, 0.15615925748400469,
          0.60166634449020118, 0.78333383446002214, -1541.2227557705858,
          -0.1775428978352783, 0.79724447486489625, -0.57695733527328286,
          2156.0788558184418, 0., 0., 0., 1. ] 
    
"""

def load_camera_calibration(yaml_path: Path) -> Dict[str, Dict[str, Any]]:
    """
    Load camera calibration parameters (intrinsics, distortion, extrinsics) from an OpenCV YAML file.

    Parameters:
    -----------
    yaml_path : Path
        Path to the YAML file containing camera calibration data.

    Returns:
    --------
    cameras : dict
        A dictionary with structure:
        {
            'camera_0': {
                'camera_matrix': np.ndarray (3x3),
                'dist_coeffs': np.ndarray (1xN),
                'pose': np.ndarray (4x4),
                'image_size': (width, height),
                'distortion_type': int,
                'camera_group': int
            },
            ...
        }
    """
    fs = cv2.FileStorage(str(yaml_path), cv2.FILE_STORAGE_READ)
    nb_cameras = int(fs.getNode("nb_camera").real())

    cameras = {}

    for cam_idx in range(nb_cameras):
        cam_name = f"camera_{cam_idx}"
        cam_node = fs.getNode(cam_name)

        camera_matrix = cam_node.getNode("camera_matrix").mat()
        dist_coeffs = cam_node.getNode("distortion_vector").mat()
        pose_matrix = cam_node.getNode("camera_pose_matrix").mat()

        image_width = int(cam_node.getNode("img_width").real())
        image_height = int(cam_node.getNode("img_height").real())
        distortion_type = int(cam_node.getNode("distortion_type").real())
        camera_group = int(cam_node.getNode("camera_group").real())

        cameras[cam_name] = {
            "camera_matrix": camera_matrix,
            "dist_coeffs": dist_coeffs,
            "pose": pose_matrix,
            "image_size": (image_width, image_height),
            "distortion_type": distortion_type,
            "camera_group": camera_group,
        }

    fs.release()
    return cameras


def transform_object_pose_between_cameras(
    rvec_obj_in_src_cam: np.ndarray,
    tvec_obj_in_src_cam: np.ndarray,
    src_cam_extrinsic: np.ndarray,
    dst_cam_extrinsic: np.ndarray,
    verbose: bool = False
) -> tuple[np.ndarray, np.ndarray]:
    """
    Transform a 3D object's pose from the source camera's coordinate system 
    to the target (destination) camera's coordinate system.

    Parameters:
    ----------
    rvec_obj_in_src_cam : (3, 1) rotation vector of the object in source camera coordinates
    tvec_obj_in_src_cam : (3, 1) translation vector of the object in source camera coordinates
    src_cam_extrinsic : (4, 4) extrinsic matrix of the source camera in world coordinates
    dst_cam_extrinsic : (4, 4) extrinsic matrix of the target camera in world coordinates
    verbose : bool
        If True, prints intermediate transformation matrices

    Returns:
    -------
    rvec_obj_in_dst_cam : (3, 1) rotation vector of the object in destination camera coordinates
    tvec_obj_in_dst_cam : (3, 1) translation vector of the object in destination camera coordinates
    """

    # Convert object rotation vector to rotation matrix
    R_obj_in_src_cam = cv2.Rodrigues(rvec_obj_in_src_cam)[0]

    # Construct the 4x4 pose matrix (homogeneous) of the object in source camera frame
    T_obj_in_src_cam = np.eye(4)
    T_obj_in_src_cam[:3, :3] = R_obj_in_src_cam
    T_obj_in_src_cam[:3, 3] = tvec_obj_in_src_cam.flatten()

    # Compute the transformation from source camera to destination camera
    T_from_src_to_dst = np.linalg.inv(dst_cam_extrinsic) @ src_cam_extrinsic

    # Apply the transformation to move the object's pose into destination camera coordinates
    T_obj_in_dst_cam = T_from_src_to_dst @ T_obj_in_src_cam

    # Extract rotation and translation from the transformed pose
    R_obj_in_dst_cam = T_obj_in_dst_cam[:3, :3]
    rvec_obj_in_dst_cam = cv2.Rodrigues(R_obj_in_dst_cam)[0]
    tvec_obj_in_dst_cam = T_obj_in_dst_cam[:3, 3].reshape(3, 1)

    if verbose:
        print("T_src_to_dst:\n", T_from_src_to_dst)
        print("T_obj_in_dst_cam:\n", T_obj_in_dst_cam)
        print("rvec_obj_in_dst_cam:\n", rvec_obj_in_dst_cam)
        print("tvec_obj_in_dst_cam:\n", tvec_obj_in_dst_cam)
        
    return rvec_obj_in_dst_cam, tvec_obj_in_dst_cam


def pipeline(path, dst_camera_matrix, dst_dist_coeffs, rvec_obj_in_dst_cam, tvec_obj_in_dst_cam, freeze=0, verbose=False):
    path = str(path)
 
    # Create a window named
    winname = "Chessboard Corners"
    cv2.namedWindow(winname, cv2.WINDOW_NORMAL)
    cv2.resizeWindow(winname, width=HD[0], height=HD[1])
    cv2.moveWindow(winname, x=FHD[0]//2-HD[0]//2, y=FHD[1]//4-HD[1]//4)

    # Load image
    img = cv2.imread(path)

    # From 3D to 2D
    proj_points = cv2.projectPoints(
        objp_tv,
        rvec_obj_in_dst_cam,
        tvec_obj_in_dst_cam,
        dst_camera_matrix,
        dst_dist_coeffs,
    )[0].reshape(-1, 2)

    # Draw projected points
    for p in proj_points:
        cv2.circle(img, (int(p[0]), int(p[1])), 5, (0, 0, 255), -1)
    
    if verbose:
        cv2.drawFrameAxes(img, dst_camera_matrix, dst_dist_coeffs, rvec_obj_in_dst_cam, tvec_obj_in_dst_cam, 0.3, thickness=3)
        cv2.imshow(winname, img)
        cv2.waitKey(freeze)
        cv2.destroyAllWindows()


def main():
    folder = "boards_imgs"
    yaml_file = Path(f"{folder}/calibrated_cameras_data.yml")
    image_files = sorted(glob.glob(os.path.join(folder, "*.png")))

    cam_params = load_camera_calibration(yaml_file)
    cam_params["net_1"] = cam_params["camera_6"]
    cam_params["net_2"] = cam_params["camera_7"]
    
    for path in image_files:
        # Pose file
        path = Path(path)
        pose_path = Path(f"{folder}/{path.stem}.npz")
        if "tv_2" not in str(path.stem):
            continue
        
        # Get camera parameters
        dst_cam_params = cam_params["net_1"] if "net_1" in str(path) else cam_params["net_2"]
        src_cam_params = cam_params["net_1"] if "net_2" in str(path) else cam_params["net_2"]
        
        dst_camera_matrix = dst_cam_params["camera_matrix"]
        dst_dist_coeffs =   dst_cam_params["dist_coeffs"]
        
        # Solve PnP to get rotation and translation vectors
        name = pose_path.stem
        foldert = pose_path.parent
        
        if "net_1" in name:
            pose_path = f"{foldert}/net_2_tv_2.npz"
        else:
            pose_path = f"{foldert}/net_1_tv_2.npz"
        
        # Load pose
        if os.path.exists(pose_path):
            board_pose = np.load(pose_path)
            board_rvec = board_pose["rvec"]
            board_tvec = board_pose["tvec"]
            board_pose.close()
        else:
            board_rvec, board_tvec = None, None
        
        rvec_obj_in_dst_cam, tvec_obj_in_dst_cam = transform_object_pose_between_cameras(
            rvec_obj_in_src_cam=board_rvec,
            tvec_obj_in_src_cam=board_tvec,
            src_cam_extrinsic=src_cam_params["pose"],
            dst_cam_extrinsic=dst_cam_params["pose"],
            verbose=False,
        )
    
        # Calculate pose
        pipeline(
            path,
            dst_camera_matrix,
            dst_dist_coeffs,
            rvec_obj_in_dst_cam,
            tvec_obj_in_dst_cam,
            freeze=0,
            verbose=True
        )


if __name__ == "__main__":
    main()
