import cv2
import numpy as np


def crop_non_black_region(undistorted_img):
    """ Remove black borders after undistortion"""
    gray = cv2.cvtColor(undistorted_img, cv2.COLOR_BGR2GRAY)
    _, thresh = cv2.threshold(gray, 1, 255, cv2.THRESH_BINARY)
    
    # Ensure it's a binary image with values 0 and 1
    bin_img = (thresh > 0).astype(np.uint8)

    rows, cols = bin_img.shape
    max_area = 0
    region = (0, 0, 0, 0)  # x, y, width, height

    # Histogram-like DP row
    height = [0] * cols

    for i in range(rows):
        for j in range(cols):
            height[j] = height[j] + 1 if bin_img[i][j] == 1 else 0

        # Find max rectangle in histogram
        stack = []
        for j in range(cols + 1):
            while stack and (j == cols or height[j] < height[stack[-1]]):
                h = height[stack.pop()]
                w = j if not stack else j - stack[-1] - 1
                area = h * w
                if area > max_area:
                    max_area = area
                    region = (
                        stack[-1] + 1 if stack else 0,
                        i - h + 1,
                        w,
                        h
                    )
            stack.append(j)

    x, y, w, h = region
    return undistorted_img[y:y+h, x:x+w], region


# Load undistorted image
undistorted = cv2.imread("undistorted/undistorted_0000.png")
undistorted = cv2.resize(undistorted, (640, 480), interpolation=cv2.INTER_LINEAR)

cv2.imshow("Before", undistorted)
# Crop
undistorted = crop_non_black_region(undistorted)

# Show
cv2.imshow("After", undistorted)

# Wait
cv2.waitKey(0)
cv2.destroyAllWindows()
